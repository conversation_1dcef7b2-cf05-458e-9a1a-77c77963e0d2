<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试API响应</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API响应测试</h1>
        
        <div class="test-section">
            <h3>测试可用任务列表API</h3>
            <button onclick="testAvailableTasks()">获取可用任务</button>
            <div id="tasksResult"></div>
        </div>
        
        <div class="test-section">
            <h3>测试数据源选择器</h3>
            <label>合约任务选择:</label>
            <select id="contractTaskSelect">
                <option value="">选择合约风险分析任务...</option>
            </select>
            <br><br>
            <label>代理任务选择:</label>
            <select id="agentTaskSelect">
                <option value="">选择代理关系分析任务...</option>
            </select>
            <br><br>
            <button onclick="loadTasksToSelectors()">加载任务到选择器</button>
        </div>
    </div>

    <script>
        async function testAvailableTasks() {
            const resultDiv = document.getElementById('tasksResult');
            resultDiv.innerHTML = '<p>正在获取任务列表...</p>';
            
            try {
                const response = await fetch('http://localhost:5005/api/user-analysis/available-tasks', {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ API调用成功</div>
                    <h4>返回数据结构:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // 保存数据供选择器使用
                window.testData = data;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ API调用失败: ${error.message}</div>
                `;
            }
        }
        
        async function loadTasksToSelectors() {
            if (!window.testData) {
                alert('请先获取任务列表数据');
                return;
            }
            
            const data = window.testData;
            
            // 更新合约任务选择器
            const contractSelect = document.getElementById('contractTaskSelect');
            contractSelect.innerHTML = '<option value="">选择合约风险分析任务...</option>';
            
            if (data.contract_tasks && Array.isArray(data.contract_tasks)) {
                data.contract_tasks.forEach(task => {
                    const option = document.createElement('option');
                    option.value = task.task_id || task.id;
                    // 测试不同的字段名
                    const taskName = task.name || task.filename || task.task_name || task.file_path || `任务_${(task.task_id || task.id)?.substring(0, 8)}`;
                    option.textContent = `${taskName} (${new Date(task.created_at).toLocaleDateString()})`;
                    contractSelect.appendChild(option);
                });
            }
            
            // 更新代理任务选择器
            const agentSelect = document.getElementById('agentTaskSelect');
            agentSelect.innerHTML = '<option value="">选择代理关系分析任务...</option>';
            
            if (data.agent_tasks && Array.isArray(data.agent_tasks)) {
                data.agent_tasks.forEach(task => {
                    const option = document.createElement('option');
                    option.value = task.task_id || task.id;
                    // 测试不同的字段名
                    const taskName = task.name || task.filename || task.task_name || task.file_path || `任务_${(task.task_id || task.id)?.substring(0, 8)}`;
                    option.textContent = `${taskName} (${new Date(task.created_at).toLocaleDateString()})`;
                    agentSelect.appendChild(option);
                });
            }
            
            alert('任务列表已加载到选择器中');
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>
